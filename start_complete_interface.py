#!/usr/bin/env python3
"""
Complete ORA-P1 Web Interface Launcher
=====================================

This script starts both the HTTP server and WebSocket proxy
needed for the complete Hume EVI web interface.

Usage:
    python start_complete_interface.py
"""

import subprocess
import webbrowser
import time
import sys
import os
import signal


def main():
    """Launch the complete web interface."""
    print("🎭 ORA-P1 - Complete Hume EVI Web Interface")
    print("=" * 50)
    
    # Check if required files exist
    required_files = ['index.html', 'evi-client.js', 'server.py', 'websocket_proxy.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        sys.exit(1)
    
    # Check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("HUME_API_KEY")
    config_id = os.getenv("HUME_CONFIG_ID")
    
    if not api_key or api_key == "PASTE_YOUR_API_KEY_HERE":
        print("❌ HUME_API_KEY not set in .env file")
        print("Please update your .env file with valid credentials")
        sys.exit(1)
    
    if not config_id or config_id == "PASTE_YOUR_CONFIG_ID_HERE":
        print("❌ HUME_CONFIG_ID not set in .env file")
        print("Please update your .env file with valid configuration ID")
        sys.exit(1)
    
    print("✅ All required files and credentials found")
    
    processes = []
    
    try:
        # Start WebSocket proxy
        print("🔌 Starting WebSocket proxy...")
        proxy_process = subprocess.Popen([
            sys.executable, 'websocket_proxy.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(proxy_process)
        
        # Wait for proxy to start
        time.sleep(2)
        
        # Start web server
        print("🌐 Starting web server...")
        server_process = subprocess.Popen([
            sys.executable, 'server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(server_process)
        
        # Wait for server to start
        time.sleep(2)
        
        # Check if both processes started successfully
        if proxy_process.poll() is None and server_process.poll() is None:
            print("✅ Both servers started successfully")
            print("🌐 Opening browser...")
            
            # Open browser
            webbrowser.open('http://localhost:8000')
            
            print("\n📋 Instructions:")
            print("1. The web interface should open in your browser")
            print("2. Your API credentials are already configured")
            print("3. Click 'Connect to EVI' to start the conversation")
            print("4. Allow microphone access when prompted")
            print("5. Start speaking to test the voice conversation")
            print("\n🔧 Services running:")
            print("   • Web Server: http://localhost:8000")
            print("   • WebSocket Proxy: ws://localhost:8001")
            print("\n⏹️  Press Ctrl+C to stop all services")
            
            # Wait for processes
            try:
                while True:
                    time.sleep(1)
                    # Check if any process died
                    for i, process in enumerate(processes):
                        if process.poll() is not None:
                            print(f"❌ Process {i} died unexpectedly")
                            return
            except KeyboardInterrupt:
                print("\n👋 Shutting down...")
                
        else:
            print("❌ Failed to start servers")
            if proxy_process.poll() is not None:
                stdout, stderr = proxy_process.communicate()
                print("Proxy error:", stderr.decode() if stderr else "Unknown")
            if server_process.poll() is not None:
                stdout, stderr = server_process.communicate()
                print("Server error:", stderr.decode() if stderr else "Unknown")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Clean up processes
        for process in processes:
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()


if __name__ == "__main__":
    main()
