<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ORA-P1 - Hume EVI Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #4a5568;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 500;
        }

        .status.disconnected {
            background: #fed7d7;
            color: #c53030;
        }

        .status.connecting {
            background: #fef5e7;
            color: #d69e2e;
        }

        .status.connected {
            background: #c6f6d5;
            color: #38a169;
        }

        .controls {
            margin: 2rem 0;
        }

        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .btn.danger {
            background: #e53e3e;
        }

        .btn.danger:hover {
            background: #c53030;
        }

        .conversation {
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            background: #f7fafc;
        }

        .message {
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 8px;
        }

        .message.user {
            background: #bee3f8;
            margin-left: 2rem;
        }

        .message.assistant {
            background: #c6f6d5;
            margin-right: 2rem;
        }

        .emotions {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .config-section {
            margin: 1rem 0;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 10px;
        }

        .config-section h3 {
            margin-bottom: 1rem;
            color: #4a5568;
        }

        .input-group {
            margin: 0.5rem 0;
            text-align: left;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
            color: #4a5568;
        }

        .input-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            font-size: 1rem;
        }

        .audio-controls {
            margin: 1rem 0;
        }

        .volume-meter {
            width: 100%;
            height: 10px;
            background: #e2e8f0;
            border-radius: 5px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .volume-level {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #f6e05e, #f56565);
            width: 0%;
            transition: width 0.1s ease;
        }

        .footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 ORA-P1</h1>
            <p>Hume EVI Integration - Voice Conversation with Emotional Intelligence</p>
        </div>

        <div class="config-section">
            <h3>🔧 Configuration</h3>
            <div class="input-group">
                <label for="apiKey">Hume API Key:</label>
                <input type="password" id="apiKey" placeholder="Enter your Hume API key">
            </div>
            <div class="input-group">
                <label for="configId">EVI Configuration ID:</label>
                <input type="text" id="configId" placeholder="Enter your EVI configuration ID">
            </div>
        </div>

        <div id="status" class="status disconnected">
            🔴 Disconnected - Click Connect to start (credentials loaded from .env)
        </div>

        <div class="controls">
            <button id="connectBtn" class="btn">🔌 Connect to EVI</button>
            <button id="disconnectBtn" class="btn danger" disabled>🔌 Disconnect</button>
        </div>

        <div class="audio-controls">
            <h3>🎤 Audio Status</h3>
            <div class="volume-meter">
                <div id="volumeLevel" class="volume-level"></div>
            </div>
            <p id="audioStatus">Microphone not initialized</p>
        </div>

        <div class="conversation" id="conversation">
            <p style="color: #666; text-align: center;">Conversation will appear here...</p>
        </div>

        <div class="footer">
            <p>🚀 Built for ORA-P1 Therapeutic AI • Powered by Hume EVI</p>
            <p>Make sure to allow microphone access when prompted</p>
        </div>
    </div>

    <script src="evi-client.js"></script>
</body>
</html>
