#!/usr/bin/env python3
"""
WebSocket Proxy for Hume EVI Browser Integration
===============================================

This proxy server handles the WebSocket connection between the browser
and Hume EVI API, managing authentication and message relay.

The browser connects to this proxy, which then connects to Hume EVI
with proper authentication and relays messages bidirectionally.
"""

import asyncio
import json
import logging
import os
import websockets
from websockets.server import serve
from websockets.client import connect
from dotenv import load_dotenv


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HumeEVIProxy:
    """WebSocket proxy for Hume EVI API."""
    
    def __init__(self):
        load_dotenv()
        self.api_key = os.getenv("HUME_API_KEY")
        self.config_id = os.getenv("HUME_CONFIG_ID")
        
        if not self.api_key or not self.config_id:
            raise ValueError("Missing HUME_API_KEY or HUME_CONFIG_ID in environment")
    
    async def handle_client(self, websocket, path):
        """Handle incoming client WebSocket connection."""
        logger.info(f"Client connected from {websocket.remote_address}")
        
        # Connect to Hume EVI
        hume_uri = f"wss://api.hume.ai/v0/evi/chat?api_key={self.api_key}&config_id={self.config_id}"
        
        try:
            async with connect(hume_uri) as hume_ws:
                logger.info("Connected to Hume EVI")
                
                # Start bidirectional message relay
                await asyncio.gather(
                    self.relay_client_to_hume(websocket, hume_ws),
                    self.relay_hume_to_client(hume_ws, websocket),
                    return_exceptions=True
                )
                
        except Exception as e:
            logger.error(f"Error connecting to Hume EVI: {e}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Failed to connect to Hume EVI: {str(e)}"
            }))
        finally:
            logger.info("Client disconnected")
    
    async def relay_client_to_hume(self, client_ws, hume_ws):
        """Relay messages from client to Hume EVI."""
        try:
            async for message in client_ws:
                logger.debug(f"Client -> Hume: {message[:100]}...")
                await hume_ws.send(message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Client connection closed")
        except Exception as e:
            logger.error(f"Error relaying client to Hume: {e}")
    
    async def relay_hume_to_client(self, hume_ws, client_ws):
        """Relay messages from Hume EVI to client."""
        try:
            async for message in hume_ws:
                logger.debug(f"Hume -> Client: {message[:100]}...")
                await client_ws.send(message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Hume connection closed")
        except Exception as e:
            logger.error(f"Error relaying Hume to client: {e}")


async def main():
    """Start the WebSocket proxy server."""
    proxy = HumeEVIProxy()
    
    # Start server on localhost:8001
    host = "localhost"
    port = 8001
    
    logger.info(f"Starting WebSocket proxy on ws://{host}:{port}")
    logger.info("This proxy will relay WebSocket connections to Hume EVI")
    
    async with serve(proxy.handle_client, host, port):
        logger.info(f"✅ WebSocket proxy running on ws://{host}:{port}")
        logger.info("🌐 Update your browser client to connect to this proxy")
        logger.info("⏹️  Press Ctrl+C to stop")
        
        # Keep the server running
        await asyncio.Future()  # Run forever


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Proxy server stopped by user")
    except Exception as e:
        logger.error(f"❌ Proxy server error: {e}")
