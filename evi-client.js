/**
 * Hume EVI Client for Browser Integration
 * =====================================
 * 
 * This JavaScript client handles:
 * - WebSocket connection to Hume EVI API
 * - Microphone audio capture and streaming
 * - Audio playback of EVI responses
 * - Real-time emotion display
 * - Conversation management
 */

class HumeEVIClient {
    constructor() {
        this.socket = null;
        this.audioContext = null;
        this.mediaStream = null;
        this.audioProcessor = null;
        this.isConnected = false;
        this.isRecording = false;
        
        // Audio configuration
        this.sampleRate = 16000;
        this.bufferSize = 4096;
        
        // UI elements
        this.statusEl = document.getElementById('status');
        this.connectBtn = document.getElementById('connectBtn');
        this.disconnectBtn = document.getElementById('disconnectBtn');
        this.conversationEl = document.getElementById('conversation');
        this.volumeLevelEl = document.getElementById('volumeLevel');
        this.audioStatusEl = document.getElementById('audioStatus');
        this.apiKeyInput = document.getElementById('apiKey');
        this.configIdInput = document.getElementById('configId');
        
        this.initializeEventListeners();
        this.loadStoredCredentials();
    }
    
    initializeEventListeners() {
        this.connectBtn.addEventListener('click', () => this.connect());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        
        // Save credentials when they change
        this.apiKeyInput.addEventListener('change', () => this.saveCredentials());
        this.configIdInput.addEventListener('change', () => this.saveCredentials());
    }
    
    loadStoredCredentials() {
        // Load from localStorage if available
        const apiKey = localStorage.getItem('hume_api_key');
        const configId = localStorage.getItem('hume_config_id');
        
        if (apiKey) this.apiKeyInput.value = apiKey;
        if (configId) this.configIdInput.value = configId;
    }
    
    saveCredentials() {
        localStorage.setItem('hume_api_key', this.apiKeyInput.value);
        localStorage.setItem('hume_config_id', this.configIdInput.value);
    }
    
    updateStatus(message, type = 'disconnected') {
        this.statusEl.textContent = message;
        this.statusEl.className = `status ${type}`;
    }
    
    async connect() {
        const apiKey = this.apiKeyInput.value.trim();
        const configId = this.configIdInput.value.trim();
        
        if (!apiKey || !configId) {
            alert('Please enter both API Key and Configuration ID');
            return;
        }
        
        try {
            this.updateStatus('🔄 Connecting to Hume EVI...', 'connecting');
            this.connectBtn.disabled = true;
            
            // Initialize audio first
            await this.initializeAudio();
            
            // Connect to WebSocket
            await this.connectWebSocket(apiKey, configId);
            
            this.isConnected = true;
            this.updateStatus('✅ Connected to Hume EVI', 'connected');
            this.connectBtn.disabled = true;
            this.disconnectBtn.disabled = false;
            
            this.saveCredentials();
            
        } catch (error) {
            console.error('Connection failed:', error);
            this.updateStatus(`❌ Connection failed: ${error.message}`, 'disconnected');
            this.connectBtn.disabled = false;
        }
    }
    
    async initializeAudio() {
        try {
            // Request microphone access
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.sampleRate,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });
            
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });
            
            // Create audio source and processor
            const source = this.audioContext.createMediaStreamSource(this.mediaStream);
            this.audioProcessor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1);
            
            // Process audio data
            this.audioProcessor.onaudioprocess = (event) => {
                if (this.isRecording && this.socket && this.socket.readyState === WebSocket.OPEN) {
                    const inputBuffer = event.inputBuffer.getChannelData(0);
                    this.sendAudioData(inputBuffer);
                    this.updateVolumeLevel(inputBuffer);
                }
            };
            
            source.connect(this.audioProcessor);
            this.audioProcessor.connect(this.audioContext.destination);
            
            this.audioStatusEl.textContent = '🎤 Microphone ready';
            this.isRecording = true;
            
        } catch (error) {
            throw new Error(`Audio initialization failed: ${error.message}`);
        }
    }
    
    async connectWebSocket(apiKey, configId) {
        return new Promise((resolve, reject) => {
            // Connect to our local WebSocket proxy
            const wsUrl = `ws://localhost:8001`;

            this.socket = new WebSocket(wsUrl);

            this.socket.onopen = () => {
                console.log('WebSocket connected to proxy');
                resolve();
            };

            this.socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            };

            this.socket.onerror = (error) => {
                console.error('WebSocket error:', error);
                reject(new Error('WebSocket connection failed. Make sure the proxy server is running.'));
            };

            this.socket.onclose = (event) => {
                console.log('WebSocket disconnected', event.code, event.reason);
                this.isConnected = false;
                this.updateStatus('🔴 Disconnected', 'disconnected');
                this.connectBtn.disabled = false;
                this.disconnectBtn.disabled = true;
            };
        });
    }
    
    handleMessage(message) {
        console.log('Received message:', message);
        
        switch (message.type) {
            case 'chat_metadata':
                this.addToConversation(`Chat started - ID: ${message.chat_id}`, 'system');
                break;
                
            case 'user_message':
                this.addToConversation(message.message.content, 'user', message.models?.prosody?.scores);
                break;
                
            case 'assistant_message':
                this.addToConversation(message.message.content, 'assistant', message.models?.prosody?.scores);
                break;
                
            case 'audio_output':
                this.playAudio(message.data);
                break;
                
            case 'error':
                console.error('EVI Error:', message);
                this.addToConversation(`Error: ${message.message}`, 'error');
                break;
                
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    sendAudioData(audioBuffer) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) return;

        try {
            // Convert Float32Array to Int16Array (16-bit PCM)
            const int16Buffer = new Int16Array(audioBuffer.length);
            for (let i = 0; i < audioBuffer.length; i++) {
                // Clamp values to 16-bit range
                const sample = Math.max(-1, Math.min(1, audioBuffer[i]));
                int16Buffer[i] = sample * 32767;
            }

            // Convert to base64 - handle large buffers properly
            const bytes = new Uint8Array(int16Buffer.buffer);
            let binary = '';
            const chunkSize = 8192;
            for (let i = 0; i < bytes.length; i += chunkSize) {
                const chunk = bytes.subarray(i, i + chunkSize);
                binary += String.fromCharCode.apply(null, chunk);
            }
            const base64 = btoa(binary);

            // Send audio message in correct format for Hume EVI
            const message = {
                type: 'audio_input',
                data: base64
            };

            this.socket.send(JSON.stringify(message));
        } catch (error) {
            console.error('Error sending audio data:', error);
        }
    }
    
    async playAudio(base64Data) {
        try {
            // Decode base64 to array buffer
            const binaryString = atob(base64Data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Try to decode as audio data
            try {
                const audioBuffer = await this.audioContext.decodeAudioData(bytes.buffer.slice());
                const source = this.audioContext.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(this.audioContext.destination);
                source.start();
            } catch (decodeError) {
                console.log('Direct decode failed, trying alternative method');
                // Alternative: Create audio element for playback
                const blob = new Blob([bytes], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(blob);
                const audio = new Audio(audioUrl);
                audio.play().catch(e => console.error('Audio play failed:', e));

                // Clean up URL after playing
                audio.onended = () => URL.revokeObjectURL(audioUrl);
            }

        } catch (error) {
            console.error('Audio playback error:', error);
        }
    }
    
    addToConversation(content, role, emotions = null) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${role}`;
        
        const contentEl = document.createElement('div');
        contentEl.textContent = content;
        messageEl.appendChild(contentEl);
        
        if (emotions) {
            const emotionsEl = document.createElement('div');
            emotionsEl.className = 'emotions';
            emotionsEl.textContent = this.formatEmotions(emotions);
            messageEl.appendChild(emotionsEl);
        }
        
        this.conversationEl.appendChild(messageEl);
        this.conversationEl.scrollTop = this.conversationEl.scrollHeight;
    }
    
    formatEmotions(emotions) {
        if (!emotions) return '';
        
        const topEmotions = Object.entries(emotions)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([emotion, score]) => `${emotion} (${score.toFixed(2)})`)
            .join(' | ');
            
        return `🎭 ${topEmotions}`;
    }
    
    updateVolumeLevel(audioBuffer) {
        // Calculate RMS volume
        let sum = 0;
        for (let i = 0; i < audioBuffer.length; i++) {
            sum += audioBuffer[i] * audioBuffer[i];
        }
        const rms = Math.sqrt(sum / audioBuffer.length);
        const volume = Math.min(100, rms * 1000); // Scale to 0-100
        
        this.volumeLevelEl.style.width = `${volume}%`;
    }
    
    disconnect() {
        this.isRecording = false;
        
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        if (this.audioProcessor) {
            this.audioProcessor.disconnect();
            this.audioProcessor = null;
        }
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }
        
        this.isConnected = false;
        this.updateStatus('🔴 Disconnected', 'disconnected');
        this.connectBtn.disabled = false;
        this.disconnectBtn.disabled = true;
        this.audioStatusEl.textContent = 'Microphone disconnected';
    }
}

// Initialize the client when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.eviClient = new HumeEVIClient();
});
